/**
 * 通用事件处理器 - CSP安全版本
 * 处理从内联事件迁移过来的事件处理，不使用eval()或Function构造器
 */

(function() {
    'use strict';
    
    // 预定义的安全函数映射
    const safeFunctions = {
        // 窗口操作
        'window.print()': () => window.print(),
        'window.close()': () => window.close(),
        'window.history.back()': () => window.history.back(),
        'history.back()': () => history.back(),
        'location.reload()': () => location.reload(),

        // 确认对话框
        'confirm': (message) => confirm(message),

        // 表单操作
        'resetForm()': () => {
            if (typeof resetForm === 'function') resetForm();
        },

        // 通用函数调用
        'goToDate': (event) => {
            if (typeof goToDate === 'function') goToDate(event);
        },

        // 打印相关函数
        'printStockOut': (stockOutId) => {
            // 检查参数是否有效
            if (!stockOutId || stockOutId === 0 || stockOutId === '0') {
                alert('无法打印：出库单ID无效');
                return;
            }

            // 检查页面级别的 printStockOut 函数是否存在
            if (typeof window.printStockOut === 'function') {
                return window.printStockOut(stockOutId);
            }

            // 如果页面级别函数不存在，使用默认实现
            const printUrl = window.location.origin + '/stock-out/' + stockOutId + '/print';
            window.open(printUrl, '_blank', 'height=600,width=800');
        }
    };
    
    // 安全执行函数
    function safeExecute(funcCall) {
        // 清理函数调用
        funcCall = funcCall.trim();

        console.log('🔍 尝试执行函数调用:', funcCall);

        // 处理简单的函数调用
        if (safeFunctions[funcCall]) {
            console.log('✅ 使用预定义函数:', funcCall);
            return safeFunctions[funcCall]();
        }

        // 处理confirm调用 - 改进的正则表达式
        if (funcCall.includes('confirm(')) {
            console.log('🔍 检测到confirm调用:', funcCall);

            // 多种confirm调用格式的正则表达式
            const confirmPatterns = [
                /confirm\s*\(\s*['"](.*?)['"]\s*\)/,  // confirm('message')
                /confirm\s*\(\s*"([^"]*)"\s*\)/,      // confirm("message")
                /confirm\s*\(\s*'([^']*)'\s*\)/,      // confirm('message')
                /confirm\s*\(\s*([^)]+)\s*\)/         // confirm(variable)
            ];

            for (let pattern of confirmPatterns) {
                const match = funcCall.match(pattern);
                if (match) {
                    const message = match[1] || '确定要执行此操作吗？';
                    console.log('✅ 执行confirm，消息:', message);
                    return confirm(message);
                }
            }

            // 如果没有匹配到参数，使用默认消息
            console.log('⚠️ confirm调用格式不标准，使用默认消息');
            return confirm('确定要执行此操作吗？');
        }

        // 处理带参数的函数调用
        const funcMatch = funcCall.match(/^(\w+)\((.*)\)$/);
        if (funcMatch) {
            const funcName = funcMatch[1];
            const args = funcMatch[2];

            console.log('🔍 检测到函数调用:', funcName);

            // 对于printStockOut函数，优先使用页面级别的函数
            if (funcName === 'printStockOut' && typeof window.printStockOut === 'function') {
                try {
                    // 简单参数解析（仅支持字符串和数字）
                    const parsedArgs = args.split(',').map(arg => {
                        arg = arg.trim();
                        if (arg.startsWith('"') || arg.startsWith("'")) {
                            return arg.slice(1, -1);
                        }
                        if (!isNaN(arg) && arg !== '') {
                            return Number(arg);
                        }
                        return arg;
                    });
                    console.log('✅ 使用页面级别的printStockOut函数');
                    return window.printStockOut(...parsedArgs);
                } catch (e) {
                    console.warn('页面级别printStockOut函数调用失败:', e);
                }
            }

            // 检查预定义的安全函数
            if (safeFunctions[funcName]) {
                try {
                    // 简单参数解析（仅支持字符串和数字）
                    const parsedArgs = args.split(',').map(arg => {
                        arg = arg.trim();
                        if (arg.startsWith('"') || arg.startsWith("'")) {
                            return arg.slice(1, -1);
                        }
                        if (!isNaN(arg) && arg !== '') {
                            return Number(arg);
                        }
                        return arg;
                    });
                    console.log('✅ 执行预定义安全函数:', funcName);
                    return safeFunctions[funcName](...parsedArgs);
                } catch (e) {
                    console.warn('预定义函数调用失败:', funcName, e);
                }
            }

            // 检查全局函数是否存在
            if (typeof window[funcName] === 'function') {
                try {
                    // 简单参数解析（仅支持字符串和数字）
                    const parsedArgs = args.split(',').map(arg => {
                        arg = arg.trim();
                        if (arg.startsWith('"') || arg.startsWith("'")) {
                            return arg.slice(1, -1);
                        }
                        if (!isNaN(arg)) {
                            return Number(arg);
                        }
                        return arg;
                    });
                    console.log('✅ 执行全局函数:', funcName);
                    return window[funcName](...parsedArgs);
                } catch (e) {
                    console.warn('函数调用失败:', funcName, e);
                }
            }
        }

        // 处理return语句
        if (funcCall.startsWith('return ')) {
            const returnValue = funcCall.substring(7);
            console.log('🔍 处理return语句:', returnValue);
            return safeExecute(returnValue);
        }

        console.warn('❌ 不支持的代码执行:', funcCall);
        return false;
    }
    
    // 等待 DOM 加载完成
    document.addEventListener('DOMContentLoaded', function() {

        // 延迟绑定事件，确保页面级别的脚本已经加载
        setTimeout(function() {
            // 处理通用 onclick 事件
            document.querySelectorAll('[data-onclick]').forEach(function(element) {
                const funcCall = element.getAttribute('data-onclick');
                element.addEventListener('click', function(e) {
                    e.preventDefault();
                    try {
                        safeExecute(funcCall);
                    } catch (error) {
                        console.error('事件处理器执行失败:', error, '函数:', funcCall);
                    }
                });
            });
        }, 100); // 延迟100ms
        
        // 处理表单提交事件
        document.querySelectorAll('[data-onsubmit]').forEach(function(form) {
            const funcCall = form.getAttribute('data-onsubmit');
            form.addEventListener('submit', function(e) {
                try {
                    const result = safeExecute(funcCall);
                    if (result === false) {
                        e.preventDefault();
                    }
                } catch (error) {
                    console.error('表单提交处理器执行失败:', error);
                    e.preventDefault();
                }
            });
        });
        
        // 处理其他事件类型
        const eventTypes = ['change', 'focus', 'blur', 'mouseover', 'mouseout', 'load'];
        eventTypes.forEach(function(eventType) {
            const selector = '[data-on' + eventType + ']';
            document.querySelectorAll(selector).forEach(function(element) {
                const funcCall = element.getAttribute('data-on' + eventType);
                element.addEventListener(eventType, function(e) {
                    try {
                        safeExecute(funcCall);
                    } catch (error) {
                        console.error('事件处理器执行失败:', error, '函数:', funcCall);
                    }
                });
            });
        });
        
        console.log('✅ 通用事件处理器已加载 (CSP安全版本)');
    });
})();