#!/usr/bin/env python3
"""
查看食材分类结果
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import Ingredient, IngredientCategory

def main():
    """主函数"""
    app = create_app()
    
    with app.app_context():
        print("食材分类统计结果")
        print("=" * 60)
        
        # 获取六大分类的统计
        main_categories = ['肉类', '蔬菜类', '水果类', '谷物类', '调味品', '水产品']
        
        for category_name in main_categories:
            ingredients = Ingredient.query.filter_by(category=category_name).all()
            print(f"\n【{category_name}】({len(ingredients)}个)")
            print("-" * 40)
            
            # 显示前20个食材作为示例
            for i, ingredient in enumerate(ingredients[:20]):
                print(f"{i+1:2d}. {ingredient.name}")
            
            if len(ingredients) > 20:
                print(f"    ... 还有 {len(ingredients) - 20} 个食材")
        
        print("\n" + "=" * 60)
        print("分类整理完成！")
        print("✅ 肉类：各种肉类食材")
        print("✅ 蔬菜类：各种蔬菜食材") 
        print("✅ 水果类：各种水果食材")
        print("✅ 谷物类：谷物、豆类、坚果等")
        print("✅ 调味品：各种调味料和香料")
        print("✅ 水产品：鱼类、虾类、贝类等水产品")

if __name__ == '__main__':
    main()
