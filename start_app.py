"""
统一的应用启动脚本
提供更好的错误处理和日志记录
"""
import os
import sys
import logging
import argparse
from datetime import datetime

# 配置日志
log_dir = 'logs'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f'app_{datetime.now().strftime("%Y%m%d")}.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('app')

def check_environment():
    """检查运行环境"""
    logger.info("检查运行环境...")

    # 检查Python版本
    python_version = sys.version.split()[0]
    logger.info(f"Python版本: {python_version}")
    if not python_version.startswith('3.'):
        logger.error("需要Python 3.x版本")
        return 0

    # 检查虚拟环境
    if not os.path.exists('venv'):
        logger.warning("未检测到虚拟环境，建议使用虚拟环境运行应用")

    # 检查数据库文件
    if not os.path.exists('app.db'):
        logger.error("数据库文件不存在，请先初始化数据库")
        return 0

    # 检查配置文件
    if not os.path.exists('config.py'):
        logger.error("配置文件不存在")
        return 0

    logger.info("环境检查完成")
    return 1

def check_database():
    """检查数据库结构"""
    logger.info("检查数据库结构...")
    try:
        from app import create_app, db
        from sqlalchemy import inspect

        app = create_app()
        with app.app_context():
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            logger.info(f"数据库中的表: {', '.join(tables)}")

            # 检查关键表是否存在
            required_tables = ['users', 'roles', 'administrative_areas']
            missing_tables = [table for table in required_tables if table not in tables]

            if missing_tables:
                logger.error(f"缺少必要的表: {', '.join(missing_tables)}")
                return 0

            # 检查 administrative_areas 表是否有 is_township_school 字段
            if 'administrative_areas' in tables:
                columns = [column['name'] for column in inspector.get_columns('administrative_areas')]
                if 'is_township_school' not in columns:
                    logger.warning("administrative_areas 表缺少 is_township_school 字段")
                    return 0

            logger.info("数据库结构检查完成")
            return 1
    except Exception as e:
        logger.error(f"检查数据库时出错: {str(e)}")
        return 0

def start_app(debug=0):
    """启动应用"""
    logger.info("正在启动应用...")

    if not check_environment():
        logger.error("环境检查失败，应用无法启动")
        return 0

    if not check_database():
        logger.error("数据库检查失败，应用无法启动")
        return 0

    try:
        from app import create_app
        app = create_app()

        host = '127.0.0.1'
        port = 5000

        logger.info(f"应用正在启动，监听地址: {host}:{port}")
        app.run(debug=debug, host=host, port=port)
        return 1
    except Exception as e:
        logger.error(f"启动应用时出错: {str(e)}")
        return 0

if __name__ == "__main__":
    # 从系统设置中获取项目名称
    try:
        from app.models_system import SystemSetting
        project_name = SystemSetting.get_value('project_name', '系统')
    except:
        project_name = '系统'

    parser = argparse.ArgumentParser(description=f"启动{project_name}")
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    args = parser.parse_args()

    success = start_app(debug=args.debug)
    if not success:
        sys.exit(1)
