#!/usr/bin/env python3
"""
测试消费计划超级编辑器
"""

import requests
import sys

def test_super_editor():
    """测试消费计划超级编辑器"""
    base_url = "http://www.tdtech.xin"
    
    # 创建会话
    session = requests.Session()
    
    # 测试用户（有仓库的用户）
    test_users = [
        {"username": "18373062333", "password": "123456", "school": "朝阳区实验中学"},
        {"username": "zxxx253", "password": "123456", "school": "岳阳县相思中心学校"},
        {"username": "hqzcdyxx461", "password": "123456", "school": "海淀区中关村第一小学"}
    ]
    
    for user in test_users:
        print(f"\n=== 测试用户: {user['username']} ({user['school']}) ===")
        
        try:
            # 1. 获取登录页面（获取CSRF token）
            login_page = session.get(f"{base_url}/login")
            print(f"获取登录页面: {login_page.status_code}")
            
            # 2. 登录
            login_data = {
                "username": user["username"],
                "password": user["password"]
            }
            
            login_response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
            print(f"登录响应: {login_response.status_code}")
            
            if login_response.status_code == 302:
                print("✅ 登录成功")
                
                # 3. 访问消费计划超级编辑器
                super_editor_response = session.get(f"{base_url}/consumption-plan/super-editor")
                print(f"访问超级编辑器: {super_editor_response.status_code}")
                
                if super_editor_response.status_code == 200:
                    print("✅ 超级编辑器页面加载成功")
                    
                    # 检查页面内容
                    content = super_editor_response.text
                    if "当前学校" in content:
                        print("✅ 页面包含学校信息")
                    if "仓库" in content:
                        print("✅ 页面包含仓库信息")
                    if "未配置学校" in content:
                        print("⚠️ 页面显示未配置学校")
                    if "未配置仓库" in content:
                        print("⚠️ 页面显示未配置仓库")
                        
                elif super_editor_response.status_code == 302:
                    print("⚠️ 被重定向，可能是权限问题")
                    print(f"重定向到: {super_editor_response.headers.get('Location', 'Unknown')}")
                else:
                    print(f"❌ 访问失败: {super_editor_response.status_code}")
                    
            else:
                print(f"❌ 登录失败: {login_response.status_code}")
                if login_response.status_code == 200:
                    # 可能是用户名密码错误
                    if "用户名或密码错误" in login_response.text:
                        print("   原因: 用户名或密码错误")
                    else:
                        print("   原因: 未知")
                        
            # 4. 登出
            session.get(f"{base_url}/logout")
            
        except Exception as e:
            print(f"❌ 测试出错: {str(e)}")
            
        print("-" * 50)

if __name__ == '__main__':
    test_super_editor()
