#!/usr/bin/env python
"""
通知功能测试脚本
"""

from app import create_app
from app.services.notification_service import NotificationService
from app.models import User, Notification

def test_notification_system():
    """测试通知系统"""
    app = create_app()
    
    with app.app_context():
        print("=== 通知系统测试 ===\n")
        
        # 1. 检查用户
        users = User.query.limit(3).all()
        if not users:
            print("❌ 没有找到用户，请先创建用户")
            return
        
        print(f"✅ 找到 {len(users)} 个用户:")
        for user in users:
            print(f"   - {user.username} ({user.real_name or '无真实姓名'})")
        print()
        
        # 2. 测试发送通知给单个用户
        print("📨 测试发送通知给单个用户...")
        test_user = users[0]
        notification = NotificationService.send_to_user(
            user_id=test_user.id,
            title="测试通知",
            content="这是一条测试通知，用于验证通知系统是否正常工作。",
            notification_type=NotificationService.TYPE_SYSTEM,
            level=NotificationService.LEVEL_NORMAL
        )
        
        if notification:
            print(f"✅ 成功发送通知给用户 {test_user.username}，通知ID: {notification.id}")
        else:
            print(f"❌ 发送通知失败")
        print()
        
        # 3. 测试发送通知给多个用户
        if len(users) > 1:
            print("📨 测试发送通知给多个用户...")
            user_ids = [user.id for user in users[:2]]
            notifications = NotificationService.send_to_users(
                user_ids=user_ids,
                title="批量测试通知",
                content="这是一条批量发送的测试通知。",
                notification_type=NotificationService.TYPE_SYSTEM,
                level=NotificationService.LEVEL_IMPORTANT
            )
            
            print(f"✅ 成功发送 {len(notifications)} 条通知")
            print()
        
        # 4. 测试业务场景通知
        print("📨 测试业务场景通知...")
        
        # 库存不足预警
        inventory_notification = NotificationService.send_inventory_alert(
            user_id=test_user.id,
            ingredient_name="测试食材",
            current_stock=10.0,
            min_stock=50.0,
            unit="kg",
            warehouse_name="测试仓库"
        )
        
        if inventory_notification:
            print("✅ 库存不足预警发送成功")
        
        # 过期预警
        expiry_notification = NotificationService.send_expiry_alert(
            user_id=test_user.id,
            ingredient_name="测试食材2",
            batch_number="TEST001",
            expiry_date="2024-12-31",
            days_left=3,
            warehouse_name="测试仓库"
        )
        
        if expiry_notification:
            print("✅ 过期预警发送成功")
        
        # 健康证预警
        health_notification = NotificationService.send_health_cert_alert(
            user_id=test_user.id,
            employee_name="测试员工",
            cert_number="TEST_CERT_001",
            expiry_date="2024-12-31",
            days_left=15
        )
        
        if health_notification:
            print("✅ 健康证预警发送成功")
        print()
        
        # 5. 检查用户的通知数量
        print("📊 检查用户通知状态...")
        for user in users:
            unread_count = user.unread_notifications_count
            total_notifications = user.notifications.count()
            print(f"   - {user.username}: 总通知 {total_notifications} 条，未读 {unread_count} 条")
        print()
        
        # 6. 测试标记已读
        print("📖 测试标记通知为已读...")
        if notification:
            success = NotificationService.mark_as_read(notification.id, test_user.id)
            if success:
                print(f"✅ 成功标记通知 {notification.id} 为已读")
            else:
                print(f"❌ 标记通知已读失败")
        print()
        
        # 7. 显示最近的通知
        print("📋 最近的通知:")
        recent_notifications = Notification.query.order_by(Notification.created_at.desc()).limit(5).all()
        for notif in recent_notifications:
            status = "已读" if notif.is_read else "未读"
            level_name = {0: "普通", 1: "重要", 2: "紧急"}.get(notif.level, "未知")
            print(f"   - [{status}] [{level_name}] {notif.title} (用户: {notif.user.username})")
        
        print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_notification_system()
