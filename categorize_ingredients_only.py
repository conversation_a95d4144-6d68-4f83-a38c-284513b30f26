#!/usr/bin/env python3
"""
食材分类整理脚本（仅分类，不删除重复）
重新组织食材分类为：肉类、蔬菜类、水果类、谷物类、调味品、水产品
"""

import os
import sys
import re

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import Ingredient, IngredientCategory

def extract_chinese_chars(text):
    """提取文本中的汉字"""
    if not text:
        return ""
    chinese_chars = re.findall(r'[\u4e00-\u9fff]+', text)
    return ''.join(chinese_chars)

def categorize_ingredient(name):
    """根据食材名称自动分类"""
    chinese_name = extract_chinese_chars(name).lower()
    
    # 肉类关键词
    meat_keywords = [
        '肉', '猪', '牛', '羊', '鸡', '鸭', '鹅', '兔', '鸽', '火腿', '香肠', '腊肉', 
        '培根', '牛排', '猪排', '鸡翅', '鸡腿', '鸡胸', '里脊', '五花', '排骨',
        '牛腩', '羊肉', '鸡肉', '鸭肉', '猪肉', '牛肉', '禽肉', '畜肉', '蹄', '爪',
        '腰子', '蝎子', '舌', '眼肉', '肩胛', '腱子', '尾肉', '腩肉', '脊骨', '肋排'
    ]
    
    # 蔬菜类关键词
    vegetable_keywords = [
        '菜', '白菜', '青菜', '菠菜', '韭菜', '芹菜', '生菜', '油菜', '小白菜',
        '萝卜', '胡萝卜', '土豆', '马铃薯', '红薯', '地瓜', '山药', '芋头',
        '茄子', '番茄', '西红柿', '黄瓜', '冬瓜', '南瓜', '丝瓜', '苦瓜',
        '辣椒', '青椒', '红椒', '尖椒', '甜椒', '洋葱', '大葱', '小葱', '葱花',
        '蒜', '大蒜', '生姜', '姜', '豆角', '四季豆', '豇豆', '扁豆',
        '豆芽', '绿豆芽', '黄豆芽', '花菜', '菜花', '西兰花', '包菜',
        '卷心菜', '莲藕', '藕', '竹笋', '笋', '蘑菇', '香菇', '平菇',
        '金针菇', '木耳', '银耳', '海带', '紫菜', '菌', '菇', '茶树菇',
        '莲子', '蒜泥', '琵琶腿', '木瓜', '枇杷', '桑葚', '芭乐', '青柠'
    ]
    
    # 水果类关键词
    fruit_keywords = [
        '果', '苹果', '梨', '桃', '李', '杏', '樱桃', '葡萄', '草莓',
        '西瓜', '哈密瓜', '香瓜', '甜瓜', '柚子', '橙子', '橘子', '柑',
        '柠檬', '香蕉', '菠萝', '芒果', '猕猴桃', '火龙果', '榴莲',
        '荔枝', '龙眼', '桂圆', '枣', '红枣', '柿子', '石榴', '山楂',
        '雪莲果', '冬枣', '奶油草莓', '青柠檬', '贵妃芒果', '白心火龙果',
        '绿心猕猴桃', '红心火龙果', '水果柿子', '鲜柿子', '富士苹果',
        '牛奶大青枣', '桃子'
    ]
    
    # 谷物类关键词
    grain_keywords = [
        '米', '面', '粉', '麦', '豆', '大米', '小米', '糯米', '黑米',
        '面粉', '小麦', '大麦', '燕麦', '玉米', '高粱', '荞麦',
        '绿豆', '红豆', '黄豆', '黑豆', '蚕豆', '豌豆', '花生',
        '芝麻', '核桃', '杏仁', '腰果', '开心果', '瓜子', '坚果',
        '面条', '挂面', '意面', '通心粉', '面包', '馒头', '包子',
        '饺子皮', '馄饨皮', '春卷皮', '豆瓣酱'
    ]
    
    # 调味品关键词
    seasoning_keywords = [
        '盐', '糖', '醋', '酱', '油', '料酒', '生抽', '老抽', '蚝油',
        '芝麻油', '香油', '花椒', '胡椒', '八角', '桂皮', '香叶',
        '孜然', '咖喱', '辣椒粉', '胡椒粉', '五香粉', '十三香',
        '味精', '鸡精', '豆瓣酱', '甜面酱', '海鲜酱',
        '番茄酱', '沙拉酱', '蛋黄酱', '调料', '香料', '佐料',
        '食盐', '冰糖', '黑胡椒', '酱油', '中盐', '未加碘'
    ]
    
    # 水产品关键词
    seafood_keywords = [
        '鱼', '虾', '蟹', '贝', '蛤', '蚌', '螺', '鲍鱼', '海参',
        '鱿鱼', '章鱼', '带鱼', '黄鱼', '鲤鱼', '草鱼', '鲫鱼',
        '鲈鱼', '鳕鱼', '三文鱼', '金枪鱼', '龙虾', '基围虾',
        '白虾', '河虾', '海虾', '螃蟹', '大闸蟹', '扇贝', '生蚝',
        '牡蛎', '海蛎', '蛏子', '花蛤', '文蛤', '海鲜', '水产'
    ]
    
    # 按优先级检查分类
    for keyword in meat_keywords:
        if keyword in chinese_name:
            return '肉类'
    
    for keyword in seafood_keywords:
        if keyword in chinese_name:
            return '水产品'
    
    for keyword in seasoning_keywords:
        if keyword in chinese_name:
            return '调味品'
    
    for keyword in fruit_keywords:
        if keyword in chinese_name:
            return '水果类'
    
    for keyword in grain_keywords:
        if keyword in chinese_name:
            return '谷物类'
    
    for keyword in vegetable_keywords:
        if keyword in chinese_name:
            return '蔬菜类'
    
    # 默认分类为蔬菜类
    return '蔬菜类'

def reorganize_categories():
    """重新组织食材分类"""
    print("正在重新组织食材分类...")
    
    # 定义新的分类
    new_categories = [
        {'name': '肉类', 'description': '各种肉类食材'},
        {'name': '蔬菜类', 'description': '各种蔬菜食材'},
        {'name': '水果类', 'description': '各种水果食材'},
        {'name': '谷物类', 'description': '谷物、豆类、坚果等'},
        {'name': '调味品', 'description': '各种调味料和香料'},
        {'name': '水产品', 'description': '鱼类、虾类、贝类等水产品'}
    ]
    
    # 获取现有分类
    existing_categories = {cat.name: cat for cat in IngredientCategory.query.all()}
    
    # 创建或更新分类
    category_map = {}
    for cat_data in new_categories:
        if cat_data['name'] in existing_categories:
            category = existing_categories[cat_data['name']]
            category.description = cat_data['description']
            print(f"更新分类: {cat_data['name']} (ID: {category.id})")
        else:
            category = IngredientCategory(
                name=cat_data['name'],
                description=cat_data['description']
            )
            db.session.add(category)
            db.session.flush()
            print(f"创建分类: {cat_data['name']} (ID: {category.id})")
        
        category_map[cat_data['name']] = category.id
    
    db.session.commit()
    return category_map

def update_ingredient_categories(category_map):
    """更新食材分类"""
    print("正在更新食材分类...")
    
    ingredients = Ingredient.query.all()
    updated_count = 0
    
    for ingredient in ingredients:
        # 自动分类
        new_category = categorize_ingredient(ingredient.name)
        old_category = ingredient.category
        
        # 更新分类
        ingredient.category = new_category
        ingredient.category_id = category_map.get(new_category)
        
        if old_category != new_category:
            print(f"更新: {ingredient.name} ({old_category} -> {new_category})")
            updated_count += 1
    
    db.session.commit()
    print(f"更新了 {updated_count} 个食材的分类")

def main():
    """主函数"""
    app = create_app()
    
    with app.app_context():
        print("开始整理食材分类...")
        print("=" * 50)
        
        # 1. 重新组织分类
        category_map = reorganize_categories()
        
        print("\n" + "=" * 50)
        
        # 2. 更新食材分类
        update_ingredient_categories(category_map)
        
        print("\n" + "=" * 50)
        print("食材分类整理完成！")
        
        # 显示统计信息
        total_categories = IngredientCategory.query.count()
        total_ingredients = Ingredient.query.count()
        
        print(f"总分类数: {total_categories}")
        print(f"总食材数: {total_ingredients}")
        
        # 显示各分类的食材数量
        print("\n各分类食材数量:")
        for category_name in ['肉类', '蔬菜类', '水果类', '谷物类', '调味品', '水产品']:
            count = Ingredient.query.filter_by(category=category_name).count()
            print(f"  {category_name}: {count} 个")

if __name__ == '__main__':
    main()
