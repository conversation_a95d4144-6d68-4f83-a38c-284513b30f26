/**
 * 关键事件处理器
 * 专门处理删除确认、表单验证等关键功能
 */

(function() {
    'use strict';
    
    // 关键事件处理器
    const CriticalEventHandler = {
        
        // 初始化
        init: function() {
            this.bindDeleteActions();
            this.bindFormValidation();
            this.bindConfirmActions();
            console.log('✅ 关键事件处理器已初始化');
        },
        
        // 绑定删除操作
        bindDeleteActions: function() {
            // 带确认的删除操作
            document.querySelectorAll('[data-action="delete-with-confirm"]').forEach(element => {
                element.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    const confirmMessage = element.getAttribute('data-confirm-message') || '确定要删除吗？';
                    const deleteFunction = element.getAttribute('data-delete-function');
                    
                    if (window.confirm(confirmMessage)) {
                        try {
                            // 安全执行删除函数
                            this.safeExecute(deleteFunction);
                        } catch (error) {
                            console.error('删除操作执行失败:', error);
                            alert('删除操作失败，请重试');
                        }
                    }
                });
            });
            
            // 直接删除操作（添加确认）
            document.querySelectorAll('[data-action="delete-direct"]').forEach(element => {
                element.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    const confirmMessage = element.getAttribute('data-confirm-message') || '确定要删除这个项目吗？';
                    const deleteFunction = element.getAttribute('data-delete-function');
                    
                    if (confirm(confirmMessage)) {
                        try {
                            this.safeExecute(deleteFunction);
                        } catch (error) {
                            console.error('删除操作执行失败:', error);
                            alert('删除操作失败，请重试');
                        }
                    }
                });
            });
        },
        
        // 绑定表单验证
        bindFormValidation: function() {
            document.querySelectorAll('[data-validation="true"]').forEach(form => {
                form.addEventListener('submit', (e) => {
                    const submitHandler = form.getAttribute('data-submit-handler');
                    
                    try {
                        // 执行验证函数
                        const result = this.safeExecute(submitHandler);
                        
                        // 如果验证失败，阻止提交
                        if (result === false) {
                            e.preventDefault();
                            console.log('表单验证失败，已阻止提交');
                        }
                    } catch (error) {
                        console.error('表单验证执行失败:', error);
                        e.preventDefault();
                        alert('表单验证失败，请检查输入');
                    }
                });
            });
        },
        
        // 绑定确认操作
        bindConfirmActions: function() {
            // 一般确认操作
            document.querySelectorAll('[data-action="confirm"]').forEach(element => {
                element.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    const confirmHandler = element.getAttribute('data-confirm-handler');
                    
                    try {
                        this.safeExecute(confirmHandler);
                    } catch (error) {
                        console.error('确认操作执行失败:', error);
                    }
                });
            });
            
            // 返回确认操作
            document.querySelectorAll('[data-action="confirm-return"]').forEach(element => {
                element.addEventListener('click', (e) => {
                    const confirmHandler = element.getAttribute('data-confirm-handler');
                    
                    try {
                        const result = this.safeExecute(confirmHandler);
                        if (result === false) {
                            e.preventDefault();
                        }
                    } catch (error) {
                        console.error('确认操作执行失败:', error);
                        e.preventDefault();
                    }
                });
            });
        },
        
        // 安全执行代码
        safeExecute: function(code) {
            if (!code) return;
            
            try {
                // 使用 Function 构造器安全执行
                const func = createSafeFunction('return ' + code);
                return func();
            } catch (error) {
                // 如果作为表达式失败，尝试作为语句执行
                try {
                    const func = createSafeFunction(code);
                    return func();
                } catch (error2) {
                    console.error('代码执行失败:', error2);
                    throw error2;
                }
            }
        },
        
        // 为动态添加的元素重新绑定事件
        rebind: function(container) {
            if (container) {
                // 在指定容器内重新绑定
                const oldQuerySelectorAll = document.querySelectorAll;
                document.querySelectorAll = function(selector) {
                    return container.querySelectorAll(selector);
                };
                
                this.bindDeleteActions();
                this.bindFormValidation();
                this.bindConfirmActions();
                
                document.querySelectorAll = oldQuerySelectorAll;
            } else {
                // 重新绑定所有
                this.init();
            }
        }
    };
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        
    // 安全执行函数（替代eval）
    function safeExecute(code) {
        if (!code) return;
        code = code.trim();
        
        // 处理confirm调用
        if (code.includes('confirm(')) {
            const confirmMatch = code.match(/confirm\s*\(\s*['"](.*?)['"]\s*\)/);
            if (confirmMatch) {
                return confirm(confirmMatch[1]);
            }
        }
        
        console.warn('不支持的代码执行:', code);
        return false;
    }
    
    // 安全函数创建器（替代Function构造器）
    function createSafeFunction(code) {
        return function() {
            return safeExecute(code);
        };
    }



    document.addEventListener('DOMContentLoaded', () => {
            CriticalEventHandler.init();
        });
    } else {
        CriticalEventHandler.init();
    }
    
    // 暴露到全局，方便调试和动态绑定
    window.CriticalEventHandler = CriticalEventHandler;
    
})();