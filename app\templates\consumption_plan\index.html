{% extends 'base.html' %}

{% block title %}消耗计划管理{% endblock %}

{% block styles %}
{{ super() }}
<style>
/* 消耗计划页面样式 */
.compact-toolbar {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
    margin-bottom: 15px;
}

.filter-collapse {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.status-stats {
    display: flex;
    gap: 15px;
}

.status-item {
    text-align: center;
}

.status-item small {
    display: block;
    color: #6c757d;
    font-size: 11px;
}

.btn-group-compact {
    display: flex;
    gap: 2px;
}

.btn-xs {
    padding: 2px 6px;
    font-size: 11px;
    line-height: 1.2;
}

.table-compact {
    font-size: 13px;
}

.table-compact th {
    padding: 8px;
    background: #f8f9fa;
    font-weight: 600;
    font-size: 12px;
}

.table-compact td {
    padding: 8px;
    vertical-align: middle;
}

.badge-sm {
    font-size: 10px;
    padding: 2px 6px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 精简的消耗计划管理工具栏 -->
    <div class="compact-toolbar d-flex justify-content-between align-items-center">
        <!-- 左侧：页面标题和主要操作 -->
        <div class="d-flex align-items-center">
            <h4 class="mb-0 mr-3">📋 消耗计划</h4>
            <a href="{{ url_for('menu_plan.index', status='已发布') }}" class="btn btn-primary btn-sm mr-2">
                <i class="fas fa-plus"></i> 从菜单创建
            </a>
            <a href="{{ url_for('consumption_plan.new') }}" class="btn btn-success btn-sm mr-2">
                <i class="fas fa-plus-circle"></i> 直接创建
            </a>
            <a href="{{ url_for('stock_in.index') }}" class="btn btn-outline-secondary btn-sm mr-2">
                <i class="fas fa-dolly"></i> 入库管理
            </a>
            <a href="{{ url_for('stock_out.index') }}" class="btn btn-outline-primary btn-sm mr-2">
                <i class="fas fa-sign-out-alt"></i> 出库管理
            </a>
        </div>

        <!-- 中间：状态统计 -->
        <div class="status-stats">
            <div class="status-item">
                <small>计划中</small>
                <span class="badge badge-secondary">{{ plan_counts.planning or 0 }}</span>
            </div>
            <div class="status-item">
                <small>已审核</small>
                <span class="badge badge-info">{{ plan_counts.approved or 0 }}</span>
            </div>
            <div class="status-item">
                <small>已执行</small>
                <span class="badge badge-success">{{ plan_counts.executed or 0 }}</span>
            </div>
            <div class="status-item">
                <small>已取消</small>
                <span class="badge badge-danger">{{ plan_counts.cancelled or 0 }}</span>
            </div>
        </div>

        <!-- 右侧：筛选按钮 -->
        <div>
            <button type="button" class="btn btn-outline-secondary btn-sm" data-toggle="collapse" data-target="#filterForm">
                <i class="fas fa-filter"></i> 筛选
            </button>
        </div>
    </div>

    <!-- 可折叠的筛选区域 -->
    <div class="collapse mb-3" id="filterForm">
        <div class="filter-collapse">
            <form method="get" action="{{ url_for('consumption_plan.index') }}">
                <div class="row">
                    <div class="col-md-3">
                        <select name="status" class="form-control form-control-sm">
                            <option value="">全部状态</option>
                            <option value="计划中" {% if status == '计划中' %}selected{% endif %}>计划中</option>
                            <option value="已审核" {% if status == '已审核' %}selected{% endif %}>已审核</option>
                            <option value="已执行" {% if status == '已执行' %}selected{% endif %}>已执行</option>
                            <option value="已取消" {% if status == '已取消' %}selected{% endif %}>已取消</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="date" name="start_date" class="form-control form-control-sm" value="{{ start_date }}" placeholder="开始日期">
                    </div>
                    <div class="col-md-3">
                        <input type="date" name="end_date" class="form-control form-control-sm" value="{{ end_date }}" placeholder="结束日期">
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-outline-secondary btn-sm ml-1">
                            <i class="fas fa-times"></i> 清除
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 直接显示表格，无外层卡片 -->
    <div class="table-responsive">
        <table class="table table-compact table-hover table-bordered">
            <thead class="thead-light">
                <tr>
                    <th style="width: 60px;">ID</th>
                    <th style="width: 100px;">日期</th>
                    <th style="width: 80px;">餐次</th>
                    <th style="width: 80px;">人数</th>
                    <th style="width: 100px;">区域</th>
                    <th style="width: 80px;">状态</th>
                    <th style="width: 120px;">创建时间</th>
                    <th style="width: 80px;">创建人</th>
                    <th style="width: 200px;">操作</th>
                </tr>
            </thead>
                        <tbody>
                            {% for consumption_plan in consumption_plans %}
                            <tr>
                                <td class="text-center">
                                    <small class="text-muted">#{{ consumption_plan.id }}</small>
                                </td>
                                <td>
                                    <small>
                                    {% if consumption_plan.consumption_date %}
                                        {{ consumption_plan.consumption_date|format_datetime('%m-%d') }}
                                    {% elif consumption_plan.menu_plan %}
                                        {{ consumption_plan.menu_plan.plan_date|format_datetime('%m-%d') }}
                                    {% else %}
                                        -
                                    {% endif %}
                                    </small>
                                </td>
                                <td>
                                    <small>
                                    {% if consumption_plan.meal_type %}
                                        {{ consumption_plan.meal_type }}
                                    {% elif consumption_plan.menu_plan %}
                                        {{ consumption_plan.menu_plan.meal_type }}
                                    {% else %}
                                        -
                                    {% endif %}
                                    </small>
                                </td>
                                <td class="text-center">
                                    <small>
                                    {% if consumption_plan.menu_plan and consumption_plan.menu_plan.expected_diners %}
                                        {{ consumption_plan.menu_plan.expected_diners }}人
                                    {% else %}
                                        -
                                    {% endif %}
                                    </small>
                                </td>
                                <td>
                                    <small>
                                    {% if consumption_plan.menu_plan and consumption_plan.menu_plan.area and consumption_plan.menu_plan.area.name %}
                                        {{ consumption_plan.menu_plan.area.name }}
                                    {% else %}
                                        -
                                    {% endif %}
                                    </small>
                                </td>
                                <td>
                                    <span class="badge badge-{{ consumption_plan.status|status_class }} badge-sm">
                                        {{ consumption_plan.status }}
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">{{ consumption_plan.created_at|format_datetime('%m-%d %H:%M') }}</small>
                                </td>
                                <td>
                                    <small>{{ consumption_plan.creator.real_name or consumption_plan.creator.username }}</small>
                                </td>
                                <td>
                                    <div class="btn-group-compact">
                                        <a href="{{ url_for('consumption_plan.view', id=consumption_plan.id) }}"
                                           class="btn btn-xs btn-outline-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>

                                        {% if consumption_plan.status == '计划中' %}
                                        <a href="{{ url_for('consumption_plan.edit', id=consumption_plan.id) }}"
                                           class="btn btn-xs btn-outline-primary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-xs btn-outline-success" title="审核"
                                                onclick="approveConfirm('{{ url_for('consumption_plan.approve', id=consumption_plan.id) }}')">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button type="button" class="btn btn-xs btn-outline-danger" title="取消"
                                                onclick="cancelConfirm('{{ url_for('consumption_plan.cancel', id=consumption_plan.id) }}')">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                        {% endif %}

                                        {% if consumption_plan.status == '已审核' %}
                                        <button type="button" class="btn btn-xs btn-outline-warning" title="执行"
                                                onclick="executeConfirm('{{ url_for('consumption_plan.execute', id=consumption_plan.id) }}')">
                                            <i class="fas fa-dolly"></i>
                                        </button>
                                        <button type="button" class="btn btn-xs btn-outline-danger" title="取消"
                                                onclick="cancelConfirm('{{ url_for('consumption_plan.cancel', id=consumption_plan.id) }}')">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                        {% endif %}

                                        {% if consumption_plan.status == '已执行' %}
                                        <a href="{{ url_for('traceability.interface') }}?trace_type=consumption_plan&trace_id={{ consumption_plan.id }}"
                                           class="btn btn-xs btn-outline-secondary" title="溯源">
                                            <i class="fas fa-search"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="9" class="text-center">暂无消耗计划</td>
                            </tr>
                            {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- 精简分页 -->
    {% if pagination.pages > 1 %}
    <div class="d-flex justify-content-center mt-3">
        <ul class="pagination pagination-sm">
            {% if pagination.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('consumption_plan.index', page=pagination.prev_num, status=status, start_date=start_date, end_date=end_date) }}">
                    «
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">«</span>
            </li>
            {% endif %}

            {% for page in pagination.iter_pages() %}
                {% if page %}
                    {% if page != pagination.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('consumption_plan.index', page=page, status=status, start_date=start_date, end_date=end_date) }}">
                            {{ page }}
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}

            {% if pagination.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('consumption_plan.index', page=pagination.next_num, status=status, start_date=start_date, end_date=end_date) }}">
                    »
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">»</span>
            </li>
            {% endif %}
        </ul>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // 确认审核
    function approveConfirm(url) {
        if (confirm('确定要审核该消耗计划吗？')) {
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = url;
            document.body.appendChild(form);
            form.submit();
        }
    }

    // 确认执行
    function executeConfirm(url) {
        if (confirm('确定要执行该消耗计划吗？执行后将生成出库单。')) {
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = url;
            document.body.appendChild(form);
            form.submit();
        }
    }

    // 确认取消
    function cancelConfirm(url) {
        if (confirm('确定要取消该消耗计划吗？此操作不可恢复。')) {
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = url;
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
{% endblock %}
