#!/usr/bin/env python3
"""
测试审计日志修复
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.utils.log_activity import log_activity
from flask_login import login_user
from app.models import User

def test_audit_log():
    """测试审计日志功能"""
    app = create_app()
    
    with app.app_context():
        print("测试审计日志修复")
        print("=" * 50)
        
        # 获取一个用户进行测试
        user = User.query.first()
        if not user:
            print("没有找到用户，无法测试")
            return
        
        print(f"使用用户: {user.username} (ID: {user.id})")
        
        # 模拟登录
        with app.test_request_context():
            login_user(user)
            
            try:
                # 测试记录审计日志
                result = log_activity(
                    action='test',
                    resource_type='TestResource',
                    resource_id=123,
                    description='测试审计日志功能',
                    details={'test': True, 'timestamp': '2025-06-02 12:39:25'}
                )
                
                if result:
                    print(f"✅ 审计日志记录成功，ID: {result}")
                else:
                    print("❌ 审计日志记录失败")
                    
            except Exception as e:
                print(f"❌ 测试失败: {str(e)}")
        
        print("\n" + "=" * 50)
        print("测试完成！")

if __name__ == '__main__':
    test_audit_log()
