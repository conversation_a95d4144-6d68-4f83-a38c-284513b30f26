#!/usr/bin/env python3
"""
强制使用端口5000启动Flask应用的脚本
"""
import os
import sys
import time
import subprocess
import socket
from app import create_app

def kill_process_on_port(port):
    """杀死占用指定端口的进程"""
    try:
        # 使用netstat查找占用端口的进程
        result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True, shell=True)
        lines = result.stdout.split('\n')
        
        for line in lines:
            if f':{port}' in line and 'LISTENING' in line:
                parts = line.split()
                if len(parts) > 4:
                    pid = parts[-1]
                    try:
                        pid_num = int(pid)
                        if pid_num != 4:  # 不杀死系统进程
                            print(f"发现进程 {pid} 占用端口 {port}，尝试终止...")
                            subprocess.run(['taskkill', '/f', '/pid', pid], shell=True, capture_output=True)
                            time.sleep(1)
                            print(f"进程 {pid} 已终止")
                    except ValueError:
                        continue
    except Exception as e:
        print(f"清理端口时出错: {e}")

def test_port_available(port, host='127.0.0.1'):
    """测试端口是否可用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result != 0  # 如果连接失败，说明端口可用
    except Exception:
        return False

def force_start_on_port_5000():
    """强制在端口5000上启动应用"""
    port = 5000
    host = '127.0.0.1'
    
    print(f"尝试在 {host}:{port} 启动Flask应用...")
    
    # 首先尝试清理端口
    kill_process_on_port(port)
    
    # 等待一下让端口释放
    time.sleep(2)
    
    # 检查端口是否可用
    if test_port_available(port, host):
        print(f"端口 {port} 现在可用")
    else:
        print(f"端口 {port} 仍被占用，但继续尝试启动...")
    
    # 创建Flask应用
    app = create_app()
    
    try:
        print(f"正在启动Flask应用在 {host}:{port}...")
        # 禁用重载器以避免端口冲突
        app.run(debug=True, host=host, port=port, use_reloader=False, threaded=True)
    except OSError as e:
        if "Address already in use" in str(e) or "访问权限不允许" in str(e):
            print(f"端口 {port} 仍然被占用或权限不足")
            print("尝试使用备用端口...")
            
            # 尝试备用端口
            backup_ports = [5001, 5002, 5003, 8000, 8080]
            for backup_port in backup_ports:
                if test_port_available(backup_port, host):
                    print(f"使用备用端口 {backup_port}")
                    app.run(debug=True, host=host, port=backup_port, use_reloader=False, threaded=True)
                    return
            
            print("所有备用端口都不可用")
            sys.exit(1)
        else:
            raise e

if __name__ == '__main__':
    force_start_on_port_5000()
