/**
 * 认证辅助工具 - 提供统一的 CSRF 令牌支持
 */

// 登录状态缓存键名
const LOGIN_STATUS_KEY = 'isLoggedIn';
const AUTH_TOKEN_KEY = 'authToken';
const LOGIN_CHECK_INTERVAL = 5 * 60 * 1000; // 5分钟检查一次登录状态

/**
 * 初始化认证辅助工具
 */
function initAuthHelper() {
    // 设置AJAX默认头部包含CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');
    if (csrfToken) {
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                    xhr.setRequestHeader("X-CSRFToken", csrfToken);
                }
            }
        });
    }

    // 初始化登录状态
    checkAndUpdateLoginStatus();

    // 设置定期检查登录状态
    setInterval(checkAndUpdateLoginStatus, LOGIN_CHECK_INTERVAL);

    // 监听登录状态变化
    window.addEventListener('storage', function(e) {
        if (e.key === LOGIN_STATUS_KEY && e.newValue === 'false') {
            // 登录状态在其他标签页被更改
            toastr.warning('您的登录状态已变更');
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        }
    });

    console.log('认证辅助工具初始化完成');
}

/**
 * 检查并更新登录状态
 * @returns {Promise<boolean>} 登录状态
 */
function checkAndUpdateLoginStatus() {
    return new Promise((resolve) => {
        $.ajax({
            url: '/api/check-login-status',
            type: 'GET',
            success: function(response) {
                const isLoggedIn = response.isLoggedIn === true;
                updateLoginStatus(isLoggedIn);
                resolve(isLoggedIn);
            },
            error: function() {
                // 请求失败时假设未登录
                updateLoginStatus(false);
                resolve(false);
            }
        });
    });
}

/**
 * 更新登录状态缓存
 * @param {boolean} isLoggedIn 是否已登录
 */
function updateLoginStatus(isLoggedIn) {
    localStorage.setItem(LOGIN_STATUS_KEY, isLoggedIn);
}

/**
 * 获取当前登录状态
 * @returns {boolean} 是否已登录
 */
function getLoginStatus() {
    // 首先检查localStorage中的状态
    const storedStatus = localStorage.getItem(LOGIN_STATUS_KEY);
    if (storedStatus !== null) {
        return storedStatus === 'true';
    }
    
    // 如果没有缓存状态，则检查DOM元素
    return $('#user-dropdown').length > 0 || $('.user-panel').length > 0;
}

/**
 * 处理认证错误
 * @param {object} xhr XHR对象
 * @param {string} redirectPath 重定向路径，默认为当前路径
 * @returns {boolean} 是否为认证错误
 */
function handleAuthError(xhr, redirectPath) {
    if (xhr.status === 401 || xhr.status === 302) {
        const path = redirectPath || window.location.pathname;
        toastr.warning('请先登录后再执行操作');
        setTimeout(() => {
            window.location.href = `/login?next=${path}`;
        }, 1500);
        return true;
    }
    return false;
}

/**
 * 要求登录才能执行操作
 * @param {Function} callback 需要执行的回调函数
 * @param {string} message 提示消息
 */
function requireLogin(callback, message = '请先登录后再执行操作') {
    if (!getLoginStatus()) {
        const path = window.location.pathname;
        toastr.warning(message);
        
        setTimeout(() => {
            window.location.href = `/login?next=${path}`;
        }, 1500);
        return;
    }
    callback();
}

// 页面加载完成后初始化
$(document).ready(function() {
    initAuthHelper();
});
