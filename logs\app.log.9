2025-06-02 20:50:52,461 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-02 20:50:53,841 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 20:50:53,842 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 20:50:54,242 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 20:50:54,242 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 20:50:54,243 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 20:50:54,243 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 20:50:54,309 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 20:50:54,310 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 20:51:05,356 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 20:51:05,356 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 20:51:05,417 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 20:51:05,417 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 20:51:06,725 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 20:51:06,726 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 20:51:07,223 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 20:51:07,223 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 20:51:07,223 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 20:51:07,224 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 20:51:07,289 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 20:51:07,289 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 20:51:23,695 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 20:51:23,695 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 20:51:23,695 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-02', 'meal_types': ['早餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:318]
2025-06-02 20:51:23,696 INFO: 解析参数: area_id=42, consumption_date=2025-06-02, meal_types=['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:324]
2025-06-02 20:51:23,700 INFO: 查询日期: 2025-06-02, 区域: 42, 餐次: ['早餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:341]
2025-06-02 20:51:23,700 INFO: 查询餐次: 早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-02 20:51:23,718 INFO: 找到 6 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:400]
2025-06-02 20:51:23,718 INFO: 分析食谱: 鲜椒青瓜干（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:404]
2025-06-02 20:51:23,733 INFO: 食谱 鲜椒青瓜干（朝阳区实验中学版） 包含 6 种主要食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:431]
2025-06-02 20:51:23,733 INFO: 检查食材: 食用油 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:23,739 INFO: 食材 食用油: 需要 20.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:23,739 INFO: 检查食材: 盐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:23,740 INFO: 食材 盐: 需要 5.0g, 库存 0.0g [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:23,740 INFO: 检查食材: 生抽 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:23,741 INFO: 食材 生抽: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:23,741 INFO: 检查食材: 料酒 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:23,742 INFO: 食材 料酒: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:23,742 INFO: 检查食材: 老抽 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:23,743 INFO: 食材 老抽: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:23,743 INFO: 检查食材: 黄瓜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:23,744 INFO: 食材 黄瓜: 需要 100.0g, 库存 100.0g [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:23,744 INFO: 分析食谱: 黄米南瓜盅（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:404]
2025-06-02 20:51:23,750 INFO: 食谱 黄米南瓜盅（朝阳区实验中学版） 包含 6 种主要食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:431]
2025-06-02 20:51:23,751 INFO: 检查食材: 食用油 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:23,752 INFO: 食材 食用油: 需要 20.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:23,752 INFO: 检查食材: 盐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:23,753 INFO: 食材 盐: 需要 5.0g, 库存 0.0g [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:23,753 INFO: 检查食材: 生抽 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:23,754 INFO: 食材 生抽: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
