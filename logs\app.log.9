2025-06-02 20:51:29,525 INFO: 食材 生抽: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:29,530 INFO: 检查食材: 料酒 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:29,531 INFO: 食材 料酒: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:29,531 INFO: 检查食材: 老抽 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:29,532 INFO: 食材 老抽: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:29,532 INFO: 分析食谱: 缤纷吐司蒸（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:404]
2025-06-02 20:51:29,536 INFO: 食谱 缤纷吐司蒸（朝阳区实验中学版） 包含 4 种主要食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:431]
2025-06-02 20:51:29,537 INFO: 检查食材: 蒜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:29,537 INFO: 食材 蒜: 需要 210.0克, 库存 210.0克 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:29,538 INFO: 检查食材: 羊肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:29,538 INFO: 食材 羊肉: 需要 210.0克, 库存 210.0克 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:29,538 INFO: 检查食材: 吐司 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:29,539 INFO: 食材 吐司: 需要 2.0片, 库存 199.7片 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:29,539 INFO: 检查食材: 芹菜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:29,541 INFO: 食材 芹菜: 需要 1.0根, 库存 100.0根 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:37,214 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 20:51:37,214 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 20:51:38,740 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 20:51:38,740 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 20:51:38,741 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-02', 'meal_types': ['早餐', '午餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:318]
2025-06-02 20:51:38,741 INFO: 解析参数: area_id=42, consumption_date=2025-06-02, meal_types=['早餐', '午餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:324]
2025-06-02 20:51:38,742 INFO: 查询日期: 2025-06-02, 区域: 42, 餐次: ['早餐', '午餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:341]
2025-06-02 20:51:38,742 INFO: 查询餐次: 早餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-02 20:51:38,754 INFO: 找到 6 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:400]
2025-06-02 20:51:38,754 INFO: 分析食谱: 鲜椒青瓜干（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:404]
2025-06-02 20:51:38,772 INFO: 食谱 鲜椒青瓜干（朝阳区实验中学版） 包含 6 种主要食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:431]
2025-06-02 20:51:38,772 INFO: 检查食材: 食用油 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:38,777 INFO: 食材 食用油: 需要 20.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:38,777 INFO: 检查食材: 盐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:38,778 INFO: 食材 盐: 需要 5.0g, 库存 0.0g [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:38,778 INFO: 检查食材: 生抽 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:38,778 INFO: 食材 生抽: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:38,779 INFO: 检查食材: 料酒 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:38,779 INFO: 食材 料酒: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:38,779 INFO: 检查食材: 老抽 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:38,780 INFO: 食材 老抽: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:38,780 INFO: 检查食材: 黄瓜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:38,782 INFO: 食材 黄瓜: 需要 100.0g, 库存 100.0g [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:38,782 INFO: 分析食谱: 黄米南瓜盅（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:404]
2025-06-02 20:51:38,787 INFO: 食谱 黄米南瓜盅（朝阳区实验中学版） 包含 6 种主要食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:431]
2025-06-02 20:51:38,787 INFO: 检查食材: 食用油 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:38,788 INFO: 食材 食用油: 需要 20.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:38,788 INFO: 检查食材: 盐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:38,789 INFO: 食材 盐: 需要 5.0g, 库存 0.0g [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:38,789 INFO: 检查食材: 生抽 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:38,790 INFO: 食材 生抽: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:38,790 INFO: 检查食材: 料酒 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:38,791 INFO: 食材 料酒: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:38,792 INFO: 检查食材: 老抽 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:38,792 INFO: 食材 老抽: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:38,792 INFO: 检查食材: 南瓜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:38,793 INFO: 食材 南瓜: 需要 100.0g, 库存 200.0g [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:38,793 INFO: 分析食谱: 黑木耳炒山药（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:404]
2025-06-02 20:51:38,800 INFO: 食谱 黑木耳炒山药（朝阳区实验中学版） 包含 7 种主要食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:431]
2025-06-02 20:51:38,800 INFO: 检查食材: 食用油 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
