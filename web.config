<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <!-- HTTP重定向到Flask应用 -->
        <httpRedirect enabled="true" destination="http://127.0.0.1:8080" httpResponseStatus="Temporary" />

        <!-- 默认文档设置 -->
        <defaultDocument>
            <files>
                <clear />
                <add value="index.html" />
            </files>
        </defaultDocument>

        <!-- HTTP错误页面 -->
        <httpErrors errorMode="Detailed" />

        <!-- 安全头设置 -->
        <httpProtocol>
            <customHeaders>
                <add name="X-Frame-Options" value="SAMEORIGIN" />
                <add name="X-Content-Type-Options" value="nosniff" />
                <add name="X-XSS-Protection" value="1; mode=block" />
            </customHeaders>
        </httpProtocol>
    </system.webServer>
</configuration>
