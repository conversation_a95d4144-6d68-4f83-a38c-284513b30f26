2025-06-02 22:15:40,777 INFO: 步骤2: 为出库食材 '茄子' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 22:15:41,042 INFO: 步骤2: 为出库食材 '大米' 查找对应菜谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:80]
2025-06-02 22:15:41,078 INFO: 溯源完成: 找到 0 条溯源记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:137]
2025-06-02 22:15:41,341 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:41,414 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:41,419 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:41,519 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:41,526 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:41,535 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:41,537 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:41,540 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:41,544 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:41,723 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:41,729 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:41,733 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:41,766 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:41,859 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:41,919 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:42,037 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:42,073 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:42,078 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:42,721 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:42,725 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:42,792 ERROR: 获取溯源信息失败: 'StockIn' object has no attribute 'supplier' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\stock_out.py:396]
2025-06-02 22:15:50,715 WARNING: Blocked IP attempted access: ************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 22:15:50,715 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 22:15:51,171 WARNING: Blocked IP attempted access: ************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 22:15:51,180 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 22:16:45,326 WARNING: Blocked IP attempted access: ************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 22:16:45,335 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 22:16:45,757 WARNING: Blocked IP attempted access: ************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 22:16:45,757 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 22:16:46,135 WARNING: Blocked IP attempted access: ************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 22:16:46,135 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 22:16:46,415 WARNING: Blocked IP attempted access: ************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 22:16:46,415 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 22:17:05,129 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-02 22:17:05,129 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-02 22:17:05,131 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-02 22:17:05,131 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-02 22:17:26,131 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-02 22:17:26,132 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-02 22:17:26,139 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-02 22:17:26,139 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-02 22:18:28,653 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-02 22:20:00,938 INFO: 当前用户: 18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-02 22:20:00,938 INFO: 用户区域ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-02 22:20:00,954 INFO: 用户区域名称: 朝阳区实验中学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-02 22:20:00,955 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-02 22:20:18,942 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:786]
2025-06-02 22:20:41,232 WARNING: Rate limit exceeded for IP: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:92]
2025-06-02 22:20:41,232 ERROR: Security middleware error: 429 Too Many Requests: This user has exceeded an allotted request count. Try again later. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
