#!/usr/bin/env python3
"""
测试食材分类显示
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import Ingredient, IngredientCategory

def test_categories():
    """测试分类显示"""
    app = create_app()
    
    with app.app_context():
        print("测试食材分类显示")
        print("=" * 50)
        
        # 测试6大分类
        main_categories = ['肉类', '蔬菜类', '水果类', '谷物类', '调味品', '水产品']
        categories = IngredientCategory.query.filter(IngredientCategory.name.in_(main_categories)).all()
        
        print(f"6大分类查询结果: {len(categories)} 个")
        for cat in categories:
            print(f"  - {cat.name} (ID: {cat.id})")
        
        print("\n" + "=" * 50)
        
        # 测试分类分组
        ingredients_by_category = {}
        all_ingredients = Ingredient.query.order_by(Ingredient.category, Ingredient.name).limit(50).all()
        
        for ingredient in all_ingredients:
            category_name = ingredient.category or '未分类'
            # 只显示6大分类
            if category_name in main_categories:
                if category_name not in ingredients_by_category:
                    ingredients_by_category[category_name] = []
                ingredients_by_category[category_name].append(ingredient)
        
        # 按指定顺序排列分类
        ordered_ingredients_by_category = {}
        for category in main_categories:
            if category in ingredients_by_category:
                ordered_ingredients_by_category[category] = ingredients_by_category[category]
        
        print("分类分组结果:")
        for category_name, ingredients in ordered_ingredients_by_category.items():
            print(f"\n【{category_name}】({len(ingredients)}个)")
            for i, ingredient in enumerate(ingredients[:5]):  # 只显示前5个
                print(f"  {i+1}. {ingredient.name}")
            if len(ingredients) > 5:
                print(f"  ... 还有 {len(ingredients) - 5} 个")
        
        print("\n" + "=" * 50)
        print("测试完成！")

if __name__ == '__main__':
    test_categories()
