2025-06-02 20:51:23,808 INFO: 食材 芹菜: 需要 1.0根, 库存 100.0根 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:26,082 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:82]
2025-06-02 20:51:26,083 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:157]
2025-06-02 20:51:26,083 INFO: 收到食谱分析请求: {'area_id': 42, 'consumption_date': '2025-06-02', 'meal_types': ['午餐']} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:318]
2025-06-02 20:51:26,084 INFO: 解析参数: area_id=42, consumption_date=2025-06-02, meal_types=['午餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:324]
2025-06-02 20:51:26,088 INFO: 查询日期: 2025-06-02, 区域: 42, 餐次: ['午餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:341]
2025-06-02 20:51:26,088 INFO: 查询餐次: 午餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-02 20:51:26,112 INFO: 找到 7 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:400]
2025-06-02 20:51:26,113 INFO: 分析食谱: 米饭（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:404]
2025-06-02 20:51:26,129 INFO: 食谱 米饭（朝阳区实验中学版） 包含 1 种主要食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:431]
2025-06-02 20:51:26,130 INFO: 检查食材: 米饭 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:26,140 INFO: 食材 米饭: 需要 1.0克, 库存 699.8克 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:26,141 INFO: 分析食谱: 鲜笋烧仔排（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:404]
2025-06-02 20:51:26,163 INFO: 食谱 鲜笋烧仔排（朝阳区实验中学版） 包含 6 种主要食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:431]
2025-06-02 20:51:26,163 INFO: 检查食材: 食用油 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:26,178 INFO: 食材 食用油: 需要 20.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:26,178 INFO: 检查食材: 盐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:26,179 INFO: 食材 盐: 需要 5.0g, 库存 0.0g [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:26,179 INFO: 检查食材: 生抽 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:26,181 INFO: 食材 生抽: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:26,181 INFO: 检查食材: 料酒 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:26,183 INFO: 食材 料酒: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:26,183 INFO: 检查食材: 老抽 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:26,185 INFO: 食材 老抽: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:26,185 INFO: 检查食材: 竹笋 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:26,186 INFO: 食材 竹笋: 需要 100.0g, 库存 0.0g [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:26,186 INFO: 分析食谱: 黑木耳炒山药（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:404]
2025-06-02 20:51:26,241 INFO: 食谱 黑木耳炒山药（朝阳区实验中学版） 包含 7 种主要食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:431]
2025-06-02 20:51:26,241 INFO: 检查食材: 食用油 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:26,246 INFO: 食材 食用油: 需要 20.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:26,246 INFO: 检查食材: 盐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:26,246 INFO: 食材 盐: 需要 5.0g, 库存 0.0g [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:26,247 INFO: 检查食材: 生抽 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:26,247 INFO: 食材 生抽: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:26,247 INFO: 检查食材: 料酒 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:26,248 INFO: 食材 料酒: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:26,248 INFO: 检查食材: 老抽 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:26,249 INFO: 食材 老抽: 需要 10.0ml, 库存 0.0ml [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:26,249 INFO: 检查食材: 木耳 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:26,250 INFO: 食材 木耳: 需要 100.0g, 库存 400.0g [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:26,250 INFO: 检查食材: 山药 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:26,251 INFO: 食材 山药: 需要 100.0g, 库存 0.0g [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:26,252 INFO: 分析食谱: 缤纷吐司蒸（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:404]
2025-06-02 20:51:26,257 INFO: 食谱 缤纷吐司蒸（朝阳区实验中学版） 包含 4 种主要食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:431]
2025-06-02 20:51:26,257 INFO: 检查食材: 蒜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:26,258 INFO: 食材 蒜: 需要 210.0克, 库存 210.0克 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:26,258 INFO: 检查食材: 羊肉 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:26,260 INFO: 食材 羊肉: 需要 210.0克, 库存 210.0克 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:26,260 INFO: 检查食材: 吐司 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:26,261 INFO: 食材 吐司: 需要 2.0片, 库存 199.7片 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:26,261 INFO: 检查食材: 芹菜 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
2025-06-02 20:51:26,262 INFO: 食材 芹菜: 需要 1.0根, 库存 100.0根 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:465]
2025-06-02 20:51:26,262 INFO: 分析食谱: 鲜蚕豆烧大雁（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:404]
2025-06-02 20:51:26,266 INFO: 食谱 鲜蚕豆烧大雁（朝阳区实验中学版） 包含 5 种主要食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:431]
2025-06-02 20:51:26,266 INFO: 检查食材: 食用油 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:439]
