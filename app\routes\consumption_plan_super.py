from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from app.models import (
    ConsumptionPlan, ConsumptionDetail, Ingredient, Inventory, InventoryAlert,
    AdministrativeArea, Warehouse, IngredientCategory
)
from app import db
from datetime import datetime, date
import json

consumption_plan_super_bp = Blueprint('consumption_plan_super', __name__)

@consumption_plan_super_bp.route('/consumption-plan/super-editor')
@login_required
def super_editor():
    """消耗计划超级编辑器"""
    # 获取当前用户所属的学校区域
    current_area = current_user.get_current_area()
    if not current_area:
        flash('您没有关联到任何学校，无法使用消费计划功能', 'danger')
        return redirect(url_for('consumption_plan.index'))

    # 查找该学校的仓库（一个学校只有一个仓库）
    warehouse = Warehouse.query.filter_by(area_id=current_area.id, status='正常').first()
    if not warehouse:
        flash(f'学校 {current_area.name} 还没有配置仓库，请联系管理员', 'warning')
        return redirect(url_for('consumption_plan.index'))

    return render_template('consumption_plan/super_editor.html',
                          current_area=current_area,
                          warehouse=warehouse,
                          today_date=date.today().strftime('%Y-%m-%d'))

@consumption_plan_super_bp.route('/consumption-plan/get-warehouses/<int:area_id>')
@login_required
def get_warehouses(area_id):
    """根据区域获取仓库列表"""
    try:
        # 检查用户是否有权限操作该区域
        if not current_user.can_access_area_by_id(area_id):
            return jsonify({'error': '您没有权限操作该区域'}), 403

        # 获取区域信息
        area = AdministrativeArea.query.get(area_id)
        if not area:
            return jsonify({
                'error': f'未找到ID为{area_id}的区域',
                'message': '请选择有效的区域'
            }), 404

        area_name = area.name

        # 获取该区域的所有仓库
        warehouses = Warehouse.query.filter_by(area_id=area_id, status='正常').all()

        # 转换为JSON格式
        warehouses_data = []
        for warehouse in warehouses:
            warehouses_data.append({
                'id': warehouse.id,
                'name': warehouse.name,
                'location': warehouse.location,
                'manager_id': warehouse.manager_id,
                'status': warehouse.status
            })

        return jsonify({
            'success': True,
            'area_id': area_id,
            'area_name': area_name,
            'warehouses': warehouses_data
        })

    except Exception as e:
        # 记录错误并返回友好的错误信息
        current_app.logger.error(f"获取仓库列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取仓库列表失败',
            'message': str(e)
        }), 500

def safe_get_int_param(param_name, default=None):
    """安全地获取整数参数，如果转换失败返回默认值"""
    try:
        value = request.args.get(param_name)
        if value is None or value == '':
            return default
        return int(value)
    except (ValueError, TypeError):
        current_app.logger.warning(f"无效的{param_name}参数: {request.args.get(param_name)}")
        return default

@consumption_plan_super_bp.route('/consumption-plan/get_inventory_batches')
@login_required
def get_inventory_batches():
    """获取仓库中的库存批次（按FIFO排序）"""
    warehouse_id = safe_get_int_param('warehouse_id')

    if not warehouse_id:
        return jsonify({'error': '缺少仓库ID参数'}), 400

    # 检查仓库是否存在
    warehouse = Warehouse.query.get(warehouse_id)
    if not warehouse:
        return jsonify({'error': '仓库不存在'}), 404

    # 检查用户是否有权限访问该仓库
    if not current_user.can_access_area_by_id(warehouse.area_id):
        return jsonify({'error': '您没有权限访问该仓库'}), 403

    try:
        # 使用原始SQL查询避免数据类型转换问题
        from sqlalchemy import text

        sql = text("""
            SELECT
                i.id, i.ingredient_id, i.batch_number, i.supplier_id,
                i.production_date, i.expiry_date, i.quantity, i.unit,
                i.storage_location_id,
                ing.name as ingredient_name,
                cat.name as category_name,
                sup.name as supplier_name,
                sl.name as storage_location_name,
                COALESCE(si.unit_price, 0.0) as unit_price
            FROM inventories i
            JOIN ingredients ing ON i.ingredient_id = ing.id
            LEFT JOIN ingredient_categories cat ON ing.category_id = cat.id
            LEFT JOIN suppliers sup ON i.supplier_id = sup.id
            LEFT JOIN storage_locations sl ON i.storage_location_id = sl.id
            LEFT JOIN stock_in_items si ON i.batch_number = si.batch_number
                AND i.ingredient_id = si.ingredient_id
                AND i.supplier_id = si.supplier_id
            WHERE i.warehouse_id = :warehouse_id
            AND i.status = :status
            AND CAST(i.quantity AS DECIMAL(18, 2)) > :min_quantity
            ORDER BY i.production_date
        """)

        inventory_result = db.session.execute(sql, {
            'warehouse_id': warehouse_id,
            'status': '正常',
            'min_quantity': 0.0
        }).fetchall()

        # 转换为JSON格式
        result = []
        for batch in inventory_result:
            # 安全地处理日期字段
            production_date = None
            if batch.production_date:
                if hasattr(batch.production_date, 'strftime'):
                    production_date = batch.production_date.strftime('%Y-%m-%d')
                else:
                    production_date = str(batch.production_date)

            expiry_date = None
            if batch.expiry_date:
                if hasattr(batch.expiry_date, 'strftime'):
                    expiry_date = batch.expiry_date.strftime('%Y-%m-%d')
                else:
                    expiry_date = str(batch.expiry_date)

            result.append({
                'id': batch.id,
                'ingredient_id': batch.ingredient_id,
                'ingredient_name': batch.ingredient_name,
                'category': batch.category_name or '未分类',
                'batch_number': batch.batch_number,
                'supplier_id': batch.supplier_id,
                'supplier_name': batch.supplier_name,
                'production_date': production_date,
                'expiry_date': expiry_date,
                'quantity': float(batch.quantity),
                'unit': batch.unit,
                'unit_price': float(batch.unit_price) if batch.unit_price else 0.0,
                'storage_location_id': batch.storage_location_id,
                'storage_location_name': batch.storage_location_name
            })

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"获取库存批次时出错: {str(e)}")
        return jsonify({'error': '获取库存批次时出错', 'message': str(e)}), 500

@consumption_plan_super_bp.route('/consumption-plan/create_super', methods=['POST'])
@login_required
def create_super():
    """使用超级编辑器创建消耗计划"""
    try:
        # 获取表单数据
        area_id = request.form.get('area_id', type=int)
        warehouse_id = request.form.get('warehouse_id', type=int)
        consumption_date = request.form.get('consumption_date')
        meal_type = request.form.get('meal_type')
        diners_count = request.form.get('diners_count', type=int)
        notes = request.form.get('notes')

        # 调试信息
        current_app.logger.info(f"超级编辑器接收到的数据:")
        current_app.logger.info(f"  area_id: {area_id}")
        current_app.logger.info(f"  warehouse_id: {warehouse_id}")
        current_app.logger.info(f"  consumption_date: {consumption_date}")
        current_app.logger.info(f"  meal_type: {meal_type}")
        current_app.logger.info(f"  diners_count: {diners_count}")
        current_app.logger.info(f"  notes: {notes}")

        # 调试信息
        current_app.logger.info(f"超级编辑器保存数据: area_id={area_id}, warehouse_id={warehouse_id}, consumption_date={consumption_date}, meal_type={meal_type}")

        # 验证必要参数
        if not all([area_id, warehouse_id, consumption_date, meal_type]):
            flash('请填写所有必要信息', 'danger')
            return redirect(url_for('consumption_plan_super.super_editor'))

        # 检查用户是否有权限操作该区域
        if not current_user.can_access_area_by_id(area_id):
            flash('您没有权限操作该区域', 'danger')
            return redirect('http://127.0.0.1:5000/consumption-plan')

        # 获取选中的批次
        selected_batches = request.form.getlist('selected_batches[]')

        if not selected_batches:
            flash('请至少选择一个批次进行消耗', 'warning')
            return redirect('http://127.0.0.1:5000/consumption-plan/super-editor')

        # 使用原始 SQL 创建消耗计划，避免 SQLAlchemy ORM 的时间戳处理问题
        from sqlalchemy import text

        # 将日期字符串转换为 date 对象
        consumption_date_obj = datetime.strptime(consumption_date, '%Y-%m-%d').date()

        # 查找对应的菜单计划（可选）
        menu_plan_id = None
        try:
            from app.models import MenuPlan
            menu_plan = MenuPlan.query.filter_by(
                area_id=area_id,
                plan_date=consumption_date_obj,
                meal_type=meal_type
            ).first()

            if menu_plan:
                menu_plan_id = menu_plan.id
                current_app.logger.info(f"找到对应的菜单计划: ID={menu_plan_id}")
            else:
                current_app.logger.info(f"未找到对应的菜单计划，将创建独立的消耗计划: area_id={area_id}, date={consumption_date_obj}, meal_type={meal_type}")
        except Exception as e:
            current_app.logger.error(f"查找菜单计划时出错: {str(e)}")
            current_app.logger.info("将创建独立的消耗计划")

        # 完全避免使用参数绑定，直接构建 SQL 语句
        # 将日期格式化为 SQL Server 可接受的格式
        formatted_date = consumption_date_obj.strftime('%Y-%m-%d')
        diners_count_value = diners_count or 1
        # 处理字符串中的单引号，避免 SQL 注入
        meal_type_safe = meal_type.replace("'", "''") if meal_type else ''
        notes_value = (notes or '').replace("'", "''")

        sql = text(f"""
            INSERT INTO consumption_plans
            (menu_plan_id, area_id, consumption_date, meal_type, diners_count, status, created_by, notes, created_at, updated_at)
            OUTPUT inserted.id
            VALUES
            ({menu_plan_id if menu_plan_id else 'NULL'}, {area_id}, '{formatted_date}', '{meal_type_safe}', {diners_count_value}, '计划中', {current_user.id}, '{notes_value}',
            GETDATE(), GETDATE())
        """)

        result = db.session.execute(sql)
        consumption_plan_id = result.fetchone()[0]

        # 添加消耗明细
        for batch_id in selected_batches:
            quantity = request.form.get(f'quantity_{batch_id}', type=float)

            if quantity and quantity > 0:
                # 获取批次信息
                batch = Inventory.query.get(batch_id)

                if batch:
                    # 验证消耗数量不超过库存数量
                    if quantity > batch.quantity:
                        flash(f'食材 {batch.ingredient.name} (批次号: {batch.batch_number}) 的消耗数量不能超过库存数量 {batch.quantity} {batch.unit}', 'danger')
                        db.session.rollback()
                        return redirect('http://127.0.0.1:5000/consumption-plan/super-editor')

                    # 使用直接构建的 SQL 语句创建消耗明细
                    # 处理字符串中的单引号，避免 SQL 注入
                    unit_safe = batch.unit.replace("'", "''") if batch.unit else ''
                    batch_number_safe = batch.batch_number.replace("'", "''") if batch.batch_number else ''

                    # 将批次信息存储在 notes 字段中
                    notes_value = f'从批次 {batch_number_safe} 创建，库存ID: {batch.id}'
                    notes_safe = notes_value.replace("'", "''")

                    detail_sql = text(f"""
                        INSERT INTO consumption_details
                        (consumption_plan_id, ingredient_id, inventory_id, batch_number, planned_quantity, unit, status, is_main_ingredient, notes, created_at, updated_at)
                        VALUES
                        ({consumption_plan_id}, {batch.ingredient_id}, {batch.id}, '{batch_number_safe}', {quantity}, '{unit_safe}', '待出库', 1, '{notes_safe}', GETDATE(), GETDATE())
                    """)

                    db.session.execute(detail_sql)

        db.session.commit()

        # 检查库存是否足够
        from app.routes.consumption_plan import check_inventory
        check_inventory(consumption_plan_id, area_id, warehouse_id)

        flash('消耗计划创建成功', 'success')
        return redirect(url_for('consumption_plan.index'))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建消耗计划时出错: {str(e)}")
        flash(f'创建消耗计划时出错: {str(e)}', 'danger')
        return redirect(url_for('consumption_plan_super.super_editor'))

@consumption_plan_super_bp.route('/consumption-plan/analyze-recipes', methods=['POST'])
@login_required
def analyze_recipes():
    """分析食谱和食材需求"""
    try:
        data = request.get_json()
        current_app.logger.info(f"收到食谱分析请求: {data}")

        area_id = data.get('area_id')
        consumption_date = data.get('consumption_date')
        meal_types = data.get('meal_types', [])

        current_app.logger.info(f"解析参数: area_id={area_id}, consumption_date={consumption_date}, meal_types={meal_types}")

        if not all([area_id, consumption_date, meal_types]):
            current_app.logger.warning(f"缺少必要参数: area_id={area_id}, consumption_date={consumption_date}, meal_types={meal_types}")
            return jsonify({'error': '缺少必要参数'}), 400

        # 检查用户权限
        if not current_user.can_access_area_by_id(area_id):
            current_app.logger.warning(f"用户 {current_user.id} 没有权限访问区域 {area_id}")
            return jsonify({'error': '您没有权限访问该区域'}), 403

        from sqlalchemy import text
        from datetime import datetime

        # 转换日期格式
        consumption_date_obj = datetime.strptime(consumption_date, '%Y-%m-%d').date()

        current_app.logger.info(f"查询日期: {consumption_date_obj}, 区域: {area_id}, 餐次: {meal_types}")

        # 查询食谱信息
        recipes = []
        missing_ingredients = []

        for meal_type in meal_types:
            current_app.logger.info(f"查询餐次: {meal_type}")

            # 使用ORM查询避免SQL参数绑定问题
            from app.models import MenuPlan, Recipe, WeeklyMenu, WeeklyMenuRecipe

            # 先查询菜单计划中的食谱
            menu_plans = MenuPlan.query.filter(
                MenuPlan.area_id == area_id,
                MenuPlan.plan_date == consumption_date_obj,
                MenuPlan.meal_type == meal_type
            ).all()

            recipe_result = []
            if menu_plans:
                for mp in menu_plans:
                    if mp.recipe:
                        recipe_result.append({
                            'id': mp.recipe.id,
                            'recipe_name': mp.recipe.name,
                            'meal_type': mp.meal_type,
                            'expected_diners': mp.expected_diners
                        })
            else:
                # 如果菜单计划中没有找到，再查询周菜单
                # 计算星期几 (1=周日, 2=周一, ..., 7=周六)
                weekday = consumption_date_obj.weekday() + 2  # Python的weekday()是0-6，SQL Server是1-7
                if weekday > 7:
                    weekday = 1

                # 查询周菜单
                weekly_menus = WeeklyMenu.query.filter(
                    WeeklyMenu.area_id == area_id,
                    WeeklyMenu.week_start <= consumption_date_obj,
                    WeeklyMenu.week_end >= consumption_date_obj
                ).all()

                for wm in weekly_menus:
                    wmr_list = WeeklyMenuRecipe.query.filter(
                        WeeklyMenuRecipe.weekly_menu_id == wm.id,
                        WeeklyMenuRecipe.day_of_week == weekday,
                        WeeklyMenuRecipe.meal_type == meal_type
                    ).all()

                    for wmr in wmr_list:
                        if wmr.recipe:
                            recipe_result.append({
                                'id': wmr.recipe.id,
                                'recipe_name': wmr.recipe.name,
                                'meal_type': wmr.meal_type,
                                'expected_diners': getattr(wm, 'expected_diners', 0)  # 安全获取字段
                            })

            current_app.logger.info(f"找到 {len(recipe_result)} 个食谱")

            if recipe_result:
                for recipe_row in recipe_result:
                    current_app.logger.info(f"分析食谱: {recipe_row['recipe_name']}")

                    # 使用ORM查询食谱的主要食材（排除调味料）
                    from app.models import RecipeIngredient, Ingredient, IngredientCategory

                    recipe_ingredients = RecipeIngredient.query.filter(
                        RecipeIngredient.recipe_id == recipe_row['id']
                    ).all()

                    # 过滤主要食材，排除调味料
                    main_recipe_ingredients = []
                    for ri in recipe_ingredients:
                        if ri.ingredient:
                            # 检查是否为调味品
                            is_condiment = False
                            if hasattr(ri.ingredient, 'is_condiment') and ri.ingredient.is_condiment:
                                is_condiment = True
                            elif ri.ingredient.category_rel:
                                category_name = ri.ingredient.category_rel.name
                                # 排除调味料、调料、香料
                                if any(keyword in category_name for keyword in ['调味料', '调料', '香料']):
                                    is_condiment = True

                            # 只添加非调味品的食材
                            if not is_condiment:
                                main_recipe_ingredients.append(ri)

                    current_app.logger.info(f"食谱 {recipe_row['recipe_name']} 包含 {len(main_recipe_ingredients)} 种主要食材")

                    main_ingredients = []

                    for ri in main_recipe_ingredients:
                        if not ri.ingredient:
                            continue

                        current_app.logger.info(f"检查食材: {ri.ingredient.name}")

                        # 使用ORM查询库存，处理字符串类型的数量字段
                        from app.models import Inventory, Warehouse

                        # 查询所有相关库存记录
                        inventory_records = db.session.query(Inventory).join(Warehouse).filter(
                            Inventory.ingredient_id == ri.ingredient_id,
                            Warehouse.area_id == area_id,
                            Inventory.status == '正常'
                        ).all()

                        # 手动计算总量，处理字符串类型的数量
                        total_quantity = 0
                        for inv in inventory_records:
                            try:
                                qty = float(inv.quantity) if inv.quantity else 0
                                if qty > 0:
                                    total_quantity += qty
                            except (ValueError, TypeError):
                                current_app.logger.warning(f"无法转换库存数量: {inv.quantity}")
                                continue

                        available_quantity = float(total_quantity or 0)
                        required_quantity = float(ri.quantity or 0)

                        current_app.logger.info(f"食材 {ri.ingredient.name}: 需要 {required_quantity}{ri.unit}, 库存 {available_quantity}{ri.unit}")

                        is_available = available_quantity >= required_quantity

                        ingredient_info = {
                            'name': ri.ingredient.name,
                            'required_quantity': required_quantity,
                            'available_quantity': available_quantity,
                            'unit': ri.unit,
                            'available': is_available
                        }

                        main_ingredients.append(ingredient_info)

                        # 如果库存不足，添加到缺少食材列表
                        if not is_available:
                            shortage = required_quantity - available_quantity
                            missing_ingredients.append({
                                'name': ri.ingredient.name,
                                'ingredient_id': ri.ingredient_id,
                                'shortage': shortage,
                                'unit': ri.unit,
                                'meal_type': meal_type,
                                'recipe_name': recipe_row['recipe_name']
                            })

                    recipes.append({
                        'recipe_id': recipe_row['id'],
                        'recipe_name': recipe_row['recipe_name'],
                        'meal_type': recipe_row['meal_type'],
                        'expected_diners': recipe_row['expected_diners'],
                        'main_ingredients': main_ingredients
                    })
            else:
                # 没有找到食谱
                current_app.logger.info(f"餐次 {meal_type} 暂无食谱安排")
                recipes.append({
                    'recipe_id': None,
                    'recipe_name': '暂无安排',
                    'meal_type': meal_type,
                    'expected_diners': None,
                    'main_ingredients': []
                })

        # 汇总缺少的食材
        missing_summary = {}
        for missing in missing_ingredients:
            key = missing['ingredient_id']
            if key not in missing_summary:
                missing_summary[key] = {
                    'name': missing['name'],
                    'total_required': 0,
                    'unit': missing['unit'],
                    'details': []
                }
            missing_summary[key]['total_required'] += missing['shortage']
            missing_summary[key]['details'].append({
                'meal_type': missing['meal_type'],
                'recipe_name': missing['recipe_name'],
                'shortage': missing['shortage']
            })

        missing_list = list(missing_summary.values())

        return jsonify({
            'recipes': recipes,
            'missing_ingredients': missing_list,
            'total_missing_count': len(missing_list)
        })

    except Exception as e:
        current_app.logger.error(f"分析食谱时出错: {str(e)}")
        return jsonify({'error': '分析食谱时出错', 'message': str(e)}), 500





